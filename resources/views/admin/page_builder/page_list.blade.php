@extends('layouts.admin')
@push('title', get_phrase('Page Builder'))
@push('meta')@endpush
@push('css')
<style>
    .no-disabled:disabled{
        opacity: 1;
    }
</style>
@endpush
@section('content')
    <div class="ol-card radius-8px">
        <div class="ol-card-body my-3 py-12px px-20px">
            <div class="d-flex align-items-center justify-content-between gap-3 flex-wrap flex-md-nowrap">
                <h4 class="title fs-16px">
                    <i class="fi-rr-settings-sliders me-2"></i>
                    {{ get_phrase('Home Page Builder') }}
                </h4>

                <a onclick="showRightModal('{{ route('view', ['path' => 'admin.page_builder.page_create']) }}', '{{ get_phrase('Create Page') }}')" href="#"
                    class="btn ol-btn-outline-secondary d-flex align-items-center cg-10px">
                    <span class="fi-rr-plus"></span>
                    <span>{{ get_phrase('Create Page') }}</span>
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="ol-card p-4">
                <div class="ol-card-body">
                    <table class="table eTable table-hover">
                        <thead>
                            <tr>
                                <th scope="col">#</th>
                                <th scope="col">Tên trang</th>
                                <th scope="col">Builder</th>
                                <th scope="col">Trạng thái</th>
                                <th scope="col">Thao tác</th>
                            </tr>
                        </thead>

                        @foreach (App\Models\Builder_page::get() as $key => $page)
                            <tr>
                                <td>{{ ++$key }}</td>
                                <td>{{ $page->name }}</td>
                                <td>
                                    @if($page->use_grapesjs)
                                        <span class="badge bg-success">
                                            <i class="fas fa-paint-brush me-1"></i>GrapesJS
                                        </span>
                                    @else
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-code me-1"></i>Builder cũ
                                        </span>
                                    @endif
                                </td>
                                <td>
                                    <div class="eSwitches">
                                        <div class="form-check form-switch">
                                            <input
                                                onchange="actionTo('{{ route('admin.page.status', ['id' => $page->id]) }}'); pageSwitcher(this);"
                                                class="form-check-input form-switch-medium no-disabled" name="home_page" type="checkbox" @if ($page->status == 1) checked disabled @endif>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @if ($page->is_permanent == 0)
                                        <a href="{{ route('admin.page.preview', $page->id) }}" target="_blank"
                                            class="btn ol-btn-outline-secondary ol-btn-sm">Xem trước</a>

                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn ol-btn-outline-secondary ol-btn-sm dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                                Chỉnh sửa
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="{{ route('admin.page.layout.edit', ['id' => $page->id]) }}">
                                                    <i class="fas fa-code me-2"></i>Builder cũ
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item" href="{{ route('admin.page.layout.edit.grapesjs.default', ['id' => $page->id]) }}">
                                                    <i class="fas fa-star me-2"></i>GrapesJS Default (Khuyến nghị)
                                                </a></li>
                                                <li><a class="dropdown-item" href="{{ route('admin.page.layout.edit.grapesjs.minimal', ['id' => $page->id]) }}">
                                                    <i class="fas fa-cog me-2"></i>GrapesJS Minimal
                                                </a></li>
                                                <li><a class="dropdown-item" href="{{ route('admin.page.layout.edit.grapesjs.v2', ['id' => $page->id]) }}">
                                                    <i class="fas fa-cog me-2"></i>GrapesJS v2 (Ổn định)
                                                </a></li>
                                                <li><a class="dropdown-item" href="{{ route('admin.page.layout.edit.grapesjs.simple', ['id' => $page->id]) }}">
                                                    <i class="fas fa-paint-brush me-2"></i>GrapesJS (Đơn giản)
                                                </a></li>
                                                <li><a class="dropdown-item" href="{{ route('admin.page.layout.edit.grapesjs', ['id' => $page->id]) }}">
                                                    <i class="fas fa-magic me-2"></i>GrapesJS (Đầy đủ)
                                                </a></li>
                                            </ul>
                                        </div>

                                        <a class="btn ol-btn-outline-secondary ol-btn-sm"
                                            onclick="showRightModal('{{ route('view', ['path' => 'admin.page_builder.page_edit', 'id' => $page->id]) }}', 'Chỉnh sửa trang')"
                                            href="#">Cài đặt</a>
                                        <a class="btn ol-btn-outline-secondary ol-btn-sm" onclick="confirmModal('{{ route('admin.page.delete', ['id' => $page->id]) }}')"
                                            href="#" class="btn text-danger">Xóa</a>
                                    @endif
                                    @if ($page->edit_home_id == 1)
                                        <a class="btn ol-btn-outline-secondary ol-btn-sm"
                                            onclick="showRightModal('{{ route('view', ['path' => 'admin.setting.home_edit.home_edit', 'id' => $page->id]) }}', 'Chỉnh sửa trang chủ')"
                                            href="#" class="btn text-secondary">Chỉnh sửa trang chủ</a>
                                    @endif
                                </td>
                            </tr>
                        @endforeach

                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('js')
    <script>
        "use strict";

        function pageSwitcher(elem){
            $('.form-switch-medium').not(elem).prop('disabled', false);
            $('.form-switch-medium').not(elem).prop('checked', false);

            setTimeout(() => {
                $(elem).prop('checked', true);
                $(elem).prop('disabled', true);
            }, 200);
        }
    </script>
@endpush
