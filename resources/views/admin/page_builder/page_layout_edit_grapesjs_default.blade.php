<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GrapesJS Editor - Default Layout</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- GrapesJS CSS -->
    <link rel="stylesheet" href="https://unpkg.com/grapesjs/dist/css/grapes.min.css">
    <!-- GrapesJS Preset Webpage CSS -->
    <link rel="stylesheet" href="https://unpkg.com/grapesjs-preset-webpage/dist/grapesjs-preset-webpage.min.css">

    <style>
        body, html {
            margin: 0;
            padding: 0;
            height: 100vh;
            font-family: Arial, sans-serif;
        }

        .editor-header {
            background: #2c3e50;
            color: white;
            padding: 12px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            z-index: 1000;
            position: relative;
        }

        .editor-title {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
        }

        .editor-actions {
            display: flex;
            gap: 8px;
        }

        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            font-size: 13px;
            transition: background-color 0.3s;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-success:hover {
            background: #229954;
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
        }

        #gjs {
            height: calc(100vh - 50px);
            border: none;
        }

        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            font-size: 16px;
        }

        .loading-overlay.hidden {
            display: none;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            position: fixed;
            top: 60px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 4px;
            color: white;
            font-weight: 500;
            z-index: 10001;
            min-width: 300px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        }

        .alert.show {
            opacity: 1;
            transform: translateX(0);
        }

        .alert-success {
            background: #27ae60;
        }

        .alert-error {
            background: #e74c3c;
        }

        /* Custom styles for droppable columns */
        [data-gjs-droppable="true"] {
            transition: all 0.3s ease;
            position: relative;
        }

        [data-gjs-droppable="true"]:hover {
            transform: scale(1.02);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        [data-gjs-droppable="true"].gjs-hovered {
            border-style: solid !important;
            background-opacity: 0.1 !important;
        }

        [data-gjs-droppable="true"]:empty::after {
            content: "Kéo thả các thành phần vào đây";
            display: block;
            text-align: center;
            color: #999;
            font-style: italic;
            padding: 20px;
        }

        /* Improve block manager styling */
        .gjs-block {
            border-radius: 6px;
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .gjs-block:hover {
            border-color: #007bff;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        /* Canvas improvements */
        .gjs-cv-canvas {
            background: #f8f9fa;
        }

        /* Panel improvements */
        .gjs-pn-panel {
            background: #2c3e50;
        }

        .gjs-pn-btn {
            transition: all 0.2s ease;
        }

        .gjs-pn-btn:hover {
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner"></div>
        <div>Đang khởi tạo GrapesJS...</div>
    </div>

    <!-- Editor Header -->
    <div class="editor-header">
        <h1 class="editor-title">GrapesJS Editor - Trang {{ $id }}</h1>
        <div class="editor-actions">
            <button class="btn btn-primary" onclick="previewPage()" title="Xem trước">
                Xem trước
            </button>
            <button class="btn btn-success" onclick="savePage()" title="Lưu trang">
                Lưu trang
            </button>
            <a class="btn btn-secondary" href="{{ route('admin.pages') }}" title="Quay lại">
                Quay lại
            </a>
        </div>
    </div>

    <!-- GrapesJS Container -->
    <div id="gjs"></div>

    <!-- Alert Container -->
    <div id="alertContainer"></div>

    <!-- GrapesJS JavaScript -->
    <script src="https://unpkg.com/grapesjs"></script>
    <script src="https://unpkg.com/grapesjs-preset-webpage"></script>

    <script>
        let editor = null;
        const pageId = {{ $id }};

        // Wait for DOM to be fully loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, starting GrapesJS initialization...');

            // Add a small delay to ensure all resources are loaded
            setTimeout(initializeGrapesJS, 500);
        });

        function initializeGrapesJS() {
            try {
                console.log('Initializing GrapesJS with default layout...');

                // Check if GrapesJS is available
                if (typeof grapesjs === 'undefined') {
                    throw new Error('GrapesJS library not loaded');
                }

                // Initialize GrapesJS with preset-webpage for default layout
                editor = grapesjs.init({
                    container: '#gjs',
                    height: '100%',
                    width: 'auto',
                    storageManager: false,

                    // Use preset-webpage for default layout
                    plugins: ['gjs-preset-webpage'],
                    pluginsOpts: {
                        'gjs-preset-webpage': {
                            modalImportTitle: 'Nhập mã HTML',
                            modalImportLabel: '<div style="margin-bottom: 10px; font-size: 13px;">Dán mã HTML/CSS của bạn và nhấn Nhập</div>',
                            modalImportContent: function(editor) {
                                return editor.getHtml() + '<style>' + editor.getCss() + '</style>';
                            },
                            filestackOpts: false,
                            aviaryOpts: false,
                            blocksBasicOpts: {
                                blocks: ['text', 'link', 'image', 'video'],
                                flexGrid: 1,
                            },
                            customStyleManager: [{
                                name: 'General',
                                buildProps: ['float', 'display', 'position', 'top', 'right', 'left', 'bottom'],
                                properties: [{
                                    name: 'Alignment',
                                    property: 'float',
                                    type: 'radio',
                                    defaults: 'none',
                                    list: [
                                        { value: 'none', className: 'fa fa-times'},
                                        { value: 'left', className: 'fa fa-align-left'},
                                        { value: 'right', className: 'fa fa-align-right'}
                                    ],
                                },{
                                    property: 'position',
                                    type: 'select',
                                }]
                            },{
                                name: 'Dimension',
                                open: false,
                                buildProps: ['width', 'min-height', 'padding'],
                                properties: [{
                                    id: 'custom-prop',
                                    name: 'Custom Label',
                                    property: 'font-size',
                                    type: 'select',
                                    defaults: '32px',
                                    list: [
                                        { value: '12px', name: 'Tiny'},
                                        { value: '18px', name: 'Medium'},
                                        { value: '32px', name: 'Big'},
                                    ],
                                }]
                            },{
                                name: 'Typography',
                                open: false,
                                buildProps: ['font-family', 'font-size', 'font-weight', 'letter-spacing', 'color', 'line-height', 'text-align', 'text-decoration', 'text-shadow'],
                                properties: [
                                    { name: 'Font', property: 'font-family'},
                                    { name: 'Weight', property: 'font-weight'},
                                    { name: 'Font size', property: 'font-size'},
                                    { name: 'Letter spacing', property: 'letter-spacing'},
                                    { name: 'Color', property: 'color'},
                                    { name: 'Line height', property: 'line-height'},
                                    {
                                        name: 'Text align',
                                        property: 'text-align',
                                        type: 'radio',
                                        defaults: 'left',
                                        list: [
                                            { value: 'left', name: 'Left', className: 'fa fa-align-left'},
                                            { value: 'center', name: 'Center', className: 'fa fa-align-center' },
                                            { value: 'right', name: 'Right', className: 'fa fa-align-right'},
                                            { value: 'justify', name: 'Justify', className: 'fa fa-align-justify'}
                                        ],
                                    },
                                    { name: 'Text decoration', property: 'text-decoration'},
                                    { name: 'Text shadow', property: 'text-shadow'}
                                ]
                            },{
                                name: 'Decorations',
                                open: false,
                                buildProps: ['opacity', 'background-color', 'border-radius', 'border', 'box-shadow', 'background'],
                                properties: [
                                    { name: 'Opacity', property: 'opacity', type: 'slider', defaults: 1, step: 0.01, max: 1, min: 0},
                                    { name: 'Background color', property: 'background-color'},
                                    { name: 'Border radius', property: 'border-radius'},
                                    { name: 'Border', property: 'border'},
                                    { name: 'Box shadow', property: 'box-shadow'},
                                    { name: 'Background', property: 'background'},
                                ]
                            },{
                                name: 'Extra',
                                open: false,
                                buildProps: ['transition', 'perspective', 'transform'],
                                properties: [
                                    { name: 'Transition', property: 'transition'},
                                    { name: 'Perspective', property: 'perspective'},
                                    { name: 'Transform', property: 'transform'},
                                ]
                            }]
                        }
                    },

                    // Canvas configuration
                    canvas: {
                        styles: [
                            'https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css'
                        ]
                    }
                });

                // Add custom blocks for course content
                addCustomBlocks();

                // Enhance drag and drop functionality
                enhanceDragDrop();

                // Load existing content
                loadPageContent();

                // Hide loading overlay
                hideLoading();

                console.log('GrapesJS initialized successfully with default layout');
                showAlert('GrapesJS đã được khởi tạo thành công!', 'success');

            } catch (error) {
                console.error('Error initializing GrapesJS:', error);
                showError('Lỗi khởi tạo GrapesJS: ' + error.message);
            }
        }

        function addCustomBlocks() {
            const blockManager = editor.BlockManager;

            // === LAYOUT CATEGORY ===

            // 1 Column
            blockManager.add('1-column', {
                label: '1 Column',
                category: 'Layout',
                content: `
                    <div class="container">
                        <div class="row">
                            <div class="col-12" data-gjs-droppable="true" data-gjs-highlightable="true" style="min-height: 80px; padding: 20px; border: 2px dashed #007bff; background: rgba(0,123,255,0.05); text-align: center; color: #007bff;">
                                <i class="fa fa-plus-circle" style="font-size: 24px; margin-bottom: 10px;"></i><br>
                                Kéo thả các thành phần vào đây
                            </div>
                        </div>
                    </div>
                `,
                attributes: { class: 'fa fa-square-o' }
            });

            // 2 Columns 50/50
            blockManager.add('2-columns-50-50', {
                label: '2 Columns 50/50',
                category: 'Layout',
                content: `
                    <div class="container">
                        <div class="row">
                            <div class="col-md-6" data-gjs-droppable="true" data-gjs-highlightable="true" style="min-height: 80px; padding: 20px; border: 2px dashed #28a745; background: rgba(40,167,69,0.05); text-align: center; color: #28a745;">
                                <i class="fa fa-plus-circle" style="font-size: 20px; margin-bottom: 8px;"></i><br>
                                Cột trái
                            </div>
                            <div class="col-md-6" data-gjs-droppable="true" data-gjs-highlightable="true" style="min-height: 80px; padding: 20px; border: 2px dashed #28a745; background: rgba(40,167,69,0.05); text-align: center; color: #28a745;">
                                <i class="fa fa-plus-circle" style="font-size: 20px; margin-bottom: 8px;"></i><br>
                                Cột phải
                            </div>
                        </div>
                    </div>
                `,
                attributes: { class: 'fa fa-columns' }
            });

            // 2 Columns 25/75
            blockManager.add('2-columns-25-75', {
                label: '2 Columns 25/75',
                category: 'Layout',
                content: `
                    <div class="container">
                        <div class="row">
                            <div class="col-md-3" data-gjs-droppable="true" data-gjs-highlightable="true" style="min-height: 80px; padding: 15px; border: 2px dashed #ffc107; background: rgba(255,193,7,0.05); text-align: center; color: #ffc107; font-size: 14px;">
                                <i class="fa fa-plus-circle" style="font-size: 18px; margin-bottom: 5px;"></i><br>
                                Cột nhỏ
                            </div>
                            <div class="col-md-9" data-gjs-droppable="true" data-gjs-highlightable="true" style="min-height: 80px; padding: 20px; border: 2px dashed #ffc107; background: rgba(255,193,7,0.05); text-align: center; color: #ffc107;">
                                <i class="fa fa-plus-circle" style="font-size: 20px; margin-bottom: 8px;"></i><br>
                                Cột lớn
                            </div>
                        </div>
                    </div>
                `,
                attributes: { class: 'fa fa-columns' }
            });

            // 2 Columns 75/25
            blockManager.add('2-columns-75-25', {
                label: '2 Columns 75/25',
                category: 'Layout',
                content: `
                    <div class="container">
                        <div class="row">
                            <div class="col-md-9" data-gjs-droppable="true" data-gjs-highlightable="true" style="min-height: 80px; padding: 20px; border: 2px dashed #dc3545; background: rgba(220,53,69,0.05); text-align: center; color: #dc3545;">
                                <i class="fa fa-plus-circle" style="font-size: 20px; margin-bottom: 8px;"></i><br>
                                Cột lớn
                            </div>
                            <div class="col-md-3" data-gjs-droppable="true" data-gjs-highlightable="true" style="min-height: 80px; padding: 15px; border: 2px dashed #dc3545; background: rgba(220,53,69,0.05); text-align: center; color: #dc3545; font-size: 14px;">
                                <i class="fa fa-plus-circle" style="font-size: 18px; margin-bottom: 5px;"></i><br>
                                Cột nhỏ
                            </div>
                        </div>
                    </div>
                `,
                attributes: { class: 'fa fa-columns' }
            });

            // 3 Columns Equal
            blockManager.add('3-columns', {
                label: '3 Columns',
                category: 'Layout',
                content: `
                    <div class="container">
                        <div class="row">
                            <div class="col-md-4" data-gjs-droppable="true" data-gjs-highlightable="true" style="min-height: 80px; padding: 15px; border: 2px dashed #6f42c1; background: rgba(111,66,193,0.05); text-align: center; color: #6f42c1; font-size: 14px;">
                                <i class="fa fa-plus-circle" style="font-size: 18px; margin-bottom: 5px;"></i><br>
                                Cột 1
                            </div>
                            <div class="col-md-4" data-gjs-droppable="true" data-gjs-highlightable="true" style="min-height: 80px; padding: 15px; border: 2px dashed #6f42c1; background: rgba(111,66,193,0.05); text-align: center; color: #6f42c1; font-size: 14px;">
                                <i class="fa fa-plus-circle" style="font-size: 18px; margin-bottom: 5px;"></i><br>
                                Cột 2
                            </div>
                            <div class="col-md-4" data-gjs-droppable="true" data-gjs-highlightable="true" style="min-height: 80px; padding: 15px; border: 2px dashed #6f42c1; background: rgba(111,66,193,0.05); text-align: center; color: #6f42c1; font-size: 14px;">
                                <i class="fa fa-plus-circle" style="font-size: 18px; margin-bottom: 5px;"></i><br>
                                Cột 3
                            </div>
                        </div>
                    </div>
                `,
                attributes: { class: 'fa fa-th-large' }
            });

            // 3 Columns 50/25/25
            blockManager.add('3-columns-50-25-25', {
                label: '3 Columns 50/25/25',
                category: 'Layout',
                content: `
                    <div class="container">
                        <div class="row">
                            <div class="col-md-6" style="min-height: 50px; padding: 20px; border: 1px dashed #ddd;">
                                Cột lớn
                            </div>
                            <div class="col-md-3" style="min-height: 50px; padding: 20px; border: 1px dashed #ddd;">
                                Cột nhỏ 1
                            </div>
                            <div class="col-md-3" style="min-height: 50px; padding: 20px; border: 1px dashed #ddd;">
                                Cột nhỏ 2
                            </div>
                        </div>
                    </div>
                `,
                attributes: { class: 'fa fa-th-large' }
            });

            // 3 Columns 25/50/25
            blockManager.add('3-columns-25-50-25', {
                label: '3 Columns 25/50/25',
                category: 'Layout',
                content: `
                    <div class="container">
                        <div class="row">
                            <div class="col-md-3" style="min-height: 50px; padding: 20px; border: 1px dashed #ddd;">
                                Cột nhỏ 1
                            </div>
                            <div class="col-md-6" style="min-height: 50px; padding: 20px; border: 1px dashed #ddd;">
                                Cột lớn
                            </div>
                            <div class="col-md-3" style="min-height: 50px; padding: 20px; border: 1px dashed #ddd;">
                                Cột nhỏ 2
                            </div>
                        </div>
                    </div>
                `,
                attributes: { class: 'fa fa-th-large' }
            });

            // 3 Columns 25/25/50
            blockManager.add('3-columns-25-25-50', {
                label: '3 Columns 25/25/50',
                category: 'Layout',
                content: `
                    <div class="container">
                        <div class="row">
                            <div class="col-md-3" style="min-height: 50px; padding: 20px; border: 1px dashed #ddd;">
                                Cột nhỏ 1
                            </div>
                            <div class="col-md-3" style="min-height: 50px; padding: 20px; border: 1px dashed #ddd;">
                                Cột nhỏ 2
                            </div>
                            <div class="col-md-6" style="min-height: 50px; padding: 20px; border: 1px dashed #ddd;">
                                Cột lớn
                            </div>
                        </div>
                    </div>
                `,
                attributes: { class: 'fa fa-th-large' }
            });

            // 4 Columns
            blockManager.add('4-columns', {
                label: '4 Columns',
                category: 'Layout',
                content: `
                    <div class="container">
                        <div class="row">
                            <div class="col-md-3" data-gjs-droppable="true" data-gjs-highlightable="true" style="min-height: 80px; padding: 12px; border: 2px dashed #17a2b8; background: rgba(23,162,184,0.05); text-align: center; color: #17a2b8; font-size: 13px;">
                                <i class="fa fa-plus-circle" style="font-size: 16px; margin-bottom: 4px;"></i><br>
                                Cột 1
                            </div>
                            <div class="col-md-3" data-gjs-droppable="true" data-gjs-highlightable="true" style="min-height: 80px; padding: 12px; border: 2px dashed #17a2b8; background: rgba(23,162,184,0.05); text-align: center; color: #17a2b8; font-size: 13px;">
                                <i class="fa fa-plus-circle" style="font-size: 16px; margin-bottom: 4px;"></i><br>
                                Cột 2
                            </div>
                            <div class="col-md-3" data-gjs-droppable="true" data-gjs-highlightable="true" style="min-height: 80px; padding: 12px; border: 2px dashed #17a2b8; background: rgba(23,162,184,0.05); text-align: center; color: #17a2b8; font-size: 13px;">
                                <i class="fa fa-plus-circle" style="font-size: 16px; margin-bottom: 4px;"></i><br>
                                Cột 3
                            </div>
                            <div class="col-md-3" data-gjs-droppable="true" data-gjs-highlightable="true" style="min-height: 80px; padding: 12px; border: 2px dashed #17a2b8; background: rgba(23,162,184,0.05); text-align: center; color: #17a2b8; font-size: 13px;">
                                <i class="fa fa-plus-circle" style="font-size: 16px; margin-bottom: 4px;"></i><br>
                                Cột 4
                            </div>
                        </div>
                    </div>
                `,
                attributes: { class: 'fa fa-th' }
            });

            // 5 Columns
            blockManager.add('5-columns', {
                label: '5 Columns',
                category: 'Layout',
                content: `
                    <div class="container">
                        <div class="row">
                            <div class="col" style="min-height: 50px; padding: 15px; border: 1px dashed #ddd;">
                                Cột 1
                            </div>
                            <div class="col" style="min-height: 50px; padding: 15px; border: 1px dashed #ddd;">
                                Cột 2
                            </div>
                            <div class="col" style="min-height: 50px; padding: 15px; border: 1px dashed #ddd;">
                                Cột 3
                            </div>
                            <div class="col" style="min-height: 50px; padding: 15px; border: 1px dashed #ddd;">
                                Cột 4
                            </div>
                            <div class="col" style="min-height: 50px; padding: 15px; border: 1px dashed #ddd;">
                                Cột 5
                            </div>
                        </div>
                    </div>
                `,
                attributes: { class: 'fa fa-th' }
            });

            // Container Block
            blockManager.add('container', {
                label: 'Container',
                category: 'Layout',
                content: `
                    <div class="container" style="min-height: 100px; padding: 30px; border: 2px dashed #007bff; background: rgba(0,123,255,0.1);">
                        <p style="text-align: center; color: #007bff; margin: 0;">Container - Kéo thả các thành phần vào đây</p>
                    </div>
                `,
                attributes: { class: 'fa fa-square' }
            });

            // Section Block
            blockManager.add('section', {
                label: 'Section',
                category: 'Layout',
                content: `
                    <section style="min-height: 100px; padding: 40px 0; border: 2px dashed #28a745; background: rgba(40,167,69,0.1);">
                        <div class="container">
                            <p style="text-align: center; color: #28a745; margin: 0;">Section - Khu vực nội dung</p>
                        </div>
                    </section>
                `,
                attributes: { class: 'fa fa-window-maximize' }
            });

            // === KHÓA HỌC CATEGORY ===

            // Hero Section Block
            blockManager.add('hero-section', {
                label: 'Hero Section',
                category: 'Khóa học',
                content: `
                    <section class="hero-section" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 100px 0; text-align: center;">
                        <div class="container">
                            <h1 style="font-size: 3rem; margin-bottom: 20px;">Tiêu đề chính</h1>
                            <p style="font-size: 1.2rem; margin-bottom: 30px;">Mô tả ngắn gọn về sản phẩm hoặc dịch vụ của bạn</p>
                            <a href="#" class="btn btn-primary btn-lg">Bắt đầu ngay</a>
                        </div>
                    </section>
                `,
                attributes: { class: 'fa fa-star' }
            });

            // Course Card Block
            blockManager.add('course-card', {
                label: 'Thẻ khóa học',
                category: 'Khóa học',
                content: `
                    <div class="course-card" style="border: 1px solid #ddd; border-radius: 8px; overflow: hidden; margin: 20px 0; max-width: 350px;">
                        <img src="https://via.placeholder.com/350x200" alt="Course Image" style="width: 100%; height: 200px; object-fit: cover;">
                        <div style="padding: 20px;">
                            <h3 style="margin: 0 0 10px 0;">Tên khóa học</h3>
                            <p style="color: #666; margin: 0 0 15px 0;">Mô tả ngắn về khóa học</p>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <span style="font-size: 1.2rem; font-weight: bold; color: #e74c3c;">500,000 VNĐ</span>
                                <a href="#" class="btn btn-primary">Đăng ký</a>
                            </div>
                        </div>
                    </div>
                `,
                attributes: { class: 'fa fa-graduation-cap' }
            });

            // Course Grid Block
            blockManager.add('course-grid', {
                label: 'Lưới khóa học',
                category: 'Khóa học',
                content: `
                    <section style="padding: 60px 0;">
                        <div class="container">
                            <h2 style="text-align: center; margin-bottom: 50px;">Khóa học nổi bật</h2>
                            <div class="row">
                                <div class="col-md-4 mb-4">
                                    <div style="border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
                                        <img src="https://via.placeholder.com/350x200" style="width: 100%; height: 200px; object-fit: cover;">
                                        <div style="padding: 20px;">
                                            <h4>Khóa học 1</h4>
                                            <p style="color: #666;">Mô tả khóa học</p>
                                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                                <span style="font-weight: bold; color: #e74c3c;">299,000 VNĐ</span>
                                                <a href="#" class="btn btn-primary btn-sm">Xem chi tiết</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-4">
                                    <div style="border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
                                        <img src="https://via.placeholder.com/350x200" style="width: 100%; height: 200px; object-fit: cover;">
                                        <div style="padding: 20px;">
                                            <h4>Khóa học 2</h4>
                                            <p style="color: #666;">Mô tả khóa học</p>
                                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                                <span style="font-weight: bold; color: #e74c3c;">399,000 VNĐ</span>
                                                <a href="#" class="btn btn-primary btn-sm">Xem chi tiết</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-4">
                                    <div style="border: 1px solid #ddd; border-radius: 8px; overflow: hidden;">
                                        <img src="https://via.placeholder.com/350x200" style="width: 100%; height: 200px; object-fit: cover;">
                                        <div style="padding: 20px;">
                                            <h4>Khóa học 3</h4>
                                            <p style="color: #666;">Mô tả khóa học</p>
                                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                                <span style="font-weight: bold; color: #e74c3c;">499,000 VNĐ</span>
                                                <a href="#" class="btn btn-primary btn-sm">Xem chi tiết</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                `,
                attributes: { class: 'fa fa-th' }
            });

            // Testimonial Block
            blockManager.add('testimonial', {
                label: 'Đánh giá',
                category: 'Khóa học',
                content: `
                    <div class="testimonial" style="background: #f8f9fa; padding: 30px; border-radius: 8px; text-align: center; margin: 20px 0;">
                        <img src="https://via.placeholder.com/80x80" alt="Avatar" style="width: 80px; height: 80px; border-radius: 50%; margin-bottom: 20px;">
                        <blockquote style="font-style: italic; font-size: 1.1rem; margin: 0 0 20px 0;">"Đây là một khóa học tuyệt vời, tôi đã học được rất nhiều điều bổ ích."</blockquote>
                        <cite style="font-weight: bold;">Nguyễn Văn A</cite>
                        <div style="color: #ffc107; margin-top: 10px;">★★★★★</div>
                    </div>
                `,
                attributes: { class: 'fa fa-quote-left' }
            });

            // Instructor Block
            blockManager.add('instructor', {
                label: 'Giảng viên',
                category: 'Khóa học',
                content: `
                    <section style="padding: 60px 0; background: #f8f9fa;">
                        <div class="container">
                            <div class="row align-items-center">
                                <div class="col-md-4">
                                    <img src="https://via.placeholder.com/300x300" alt="Instructor" style="width: 100%; border-radius: 50%; max-width: 250px;">
                                </div>
                                <div class="col-md-8">
                                    <h2>Giảng viên chuyên nghiệp</h2>
                                    <p style="font-size: 1.1rem; color: #666; margin-bottom: 20px;">Với hơn 10 năm kinh nghiệm trong lĩnh vực giáo dục và đào tạo, chúng tôi cam kết mang đến cho bạn những kiến thức chất lượng nhất.</p>
                                    <ul style="list-style: none; padding: 0;">
                                        <li style="margin-bottom: 10px;"><i class="fa fa-check" style="color: #28a745; margin-right: 10px;"></i>Chứng chỉ quốc tế</li>
                                        <li style="margin-bottom: 10px;"><i class="fa fa-check" style="color: #28a745; margin-right: 10px;"></i>Kinh nghiệm thực tế</li>
                                        <li style="margin-bottom: 10px;"><i class="fa fa-check" style="color: #28a745; margin-right: 10px;"></i>Phương pháp giảng dạy hiện đại</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </section>
                `,
                attributes: { class: 'fa fa-user-tie' }
            });

            // Course Features Block
            blockManager.add('course-features', {
                label: 'Tính năng khóa học',
                category: 'Khóa học',
                content: `
                    <section style="padding: 60px 0;">
                        <div class="container">
                            <h2 style="text-align: center; margin-bottom: 50px;">Tại sao chọn chúng tôi?</h2>
                            <div class="row">
                                <div class="col-md-4 text-center mb-4">
                                    <div style="background: #007bff; color: white; width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; font-size: 2rem;">
                                        <i class="fa fa-video"></i>
                                    </div>
                                    <h4>Video chất lượng cao</h4>
                                    <p style="color: #666;">Học qua video HD với âm thanh rõ ràng</p>
                                </div>
                                <div class="col-md-4 text-center mb-4">
                                    <div style="background: #28a745; color: white; width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; font-size: 2rem;">
                                        <i class="fa fa-certificate"></i>
                                    </div>
                                    <h4>Chứng chỉ hoàn thành</h4>
                                    <p style="color: #666;">Nhận chứng chỉ sau khi hoàn thành khóa học</p>
                                </div>
                                <div class="col-md-4 text-center mb-4">
                                    <div style="background: #ffc107; color: white; width: 80px; height: 80px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 20px; font-size: 2rem;">
                                        <i class="fa fa-headset"></i>
                                    </div>
                                    <h4>Hỗ trợ 24/7</h4>
                                    <p style="color: #666;">Đội ngũ hỗ trợ luôn sẵn sàng giúp đỡ</p>
                                </div>
                            </div>
                        </div>
                    </section>
                `,
                attributes: { class: 'fa fa-star' }
            });

            // === FORM CATEGORY ===

            // Contact Form Block
            blockManager.add('contact-form', {
                label: 'Form liên hệ',
                category: 'Form',
                content: `
                    <section style="padding: 60px 0; background: #f8f9fa;">
                        <div class="container">
                            <div class="row">
                                <div class="col-md-8 offset-md-2">
                                    <form class="contact-form" style="background: white; padding: 40px; border-radius: 8px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
                                        <h3 style="text-align: center; margin-bottom: 30px;">Liên hệ với chúng tôi</h3>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div style="margin-bottom: 20px;">
                                                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">Họ và tên</label>
                                                    <input type="text" name="name" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px;">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div style="margin-bottom: 20px;">
                                                    <label style="display: block; margin-bottom: 5px; font-weight: bold;">Email</label>
                                                    <input type="email" name="email" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px;">
                                                </div>
                                            </div>
                                        </div>
                                        <div style="margin-bottom: 20px;">
                                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Số điện thoại</label>
                                            <input type="tel" name="phone" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px;">
                                        </div>
                                        <div style="margin-bottom: 20px;">
                                            <label style="display: block; margin-bottom: 5px; font-weight: bold;">Tin nhắn</label>
                                            <textarea name="message" rows="5" style="width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 4px; resize: vertical;"></textarea>
                                        </div>
                                        <button type="submit" style="background: #007bff; color: white; padding: 12px 30px; border: none; border-radius: 4px; cursor: pointer; width: 100%; font-size: 16px;">Gửi tin nhắn</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </section>
                `,
                attributes: { class: 'fa fa-envelope' }
            });

            // Newsletter Form
            blockManager.add('newsletter-form', {
                label: 'Form đăng ký',
                category: 'Form',
                content: `
                    <section style="background: #007bff; color: white; padding: 60px 0;">
                        <div class="container">
                            <div class="row">
                                <div class="col-md-8 offset-md-2 text-center">
                                    <h2 style="margin-bottom: 20px;">Đăng ký nhận tin tức</h2>
                                    <p style="margin-bottom: 30px; font-size: 1.1rem;">Nhận thông tin về các khóa học mới và ưu đãi đặc biệt</p>
                                    <form style="display: flex; max-width: 500px; margin: 0 auto;">
                                        <input type="email" placeholder="Nhập email của bạn" style="flex: 1; padding: 12px; border: none; border-radius: 4px 0 0 4px; font-size: 16px;">
                                        <button type="submit" style="background: #28a745; color: white; border: none; padding: 12px 20px; border-radius: 0 4px 4px 0; cursor: pointer; font-size: 16px;">Đăng ký</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </section>
                `,
                attributes: { class: 'fa fa-newspaper' }
            });

            // Statistics Block
            blockManager.add('statistics', {
                label: 'Thống kê',
                category: 'Extra',
                content: `
                    <section style="background: #2c3e50; color: white; padding: 60px 0;">
                        <div class="container">
                            <div class="row text-center">
                                <div class="col-md-3 mb-4">
                                    <div style="font-size: 3rem; font-weight: bold; margin-bottom: 10px;">1000+</div>
                                    <div style="font-size: 1.1rem;">Học viên</div>
                                </div>
                                <div class="col-md-3 mb-4">
                                    <div style="font-size: 3rem; font-weight: bold; margin-bottom: 10px;">50+</div>
                                    <div style="font-size: 1.1rem;">Khóa học</div>
                                </div>
                                <div class="col-md-3 mb-4">
                                    <div style="font-size: 3rem; font-weight: bold; margin-bottom: 10px;">20+</div>
                                    <div style="font-size: 1.1rem;">Giảng viên</div>
                                </div>
                                <div class="col-md-3 mb-4">
                                    <div style="font-size: 3rem; font-weight: bold; margin-bottom: 10px;">95%</div>
                                    <div style="font-size: 1.1rem;">Hài lòng</div>
                                </div>
                            </div>
                        </div>
                    </section>
                `,
                attributes: { class: 'fa fa-chart-bar' }
            });

            // FAQ Block
            blockManager.add('faq', {
                label: 'Câu hỏi thường gặp',
                category: 'Extra',
                content: `
                    <section style="padding: 60px 0;">
                        <div class="container">
                            <h2 style="text-align: center; margin-bottom: 50px;">Câu hỏi thường gặp</h2>
                            <div class="row">
                                <div class="col-md-8 offset-md-2">
                                    <div style="border: 1px solid #ddd; border-radius: 8px; margin-bottom: 15px;">
                                        <div style="background: #f8f9fa; padding: 20px; cursor: pointer; border-radius: 8px 8px 0 0;">
                                            <h5 style="margin: 0;">Làm thế nào để đăng ký khóa học?</h5>
                                        </div>
                                        <div style="padding: 20px; display: none;">
                                            <p>Bạn có thể đăng ký khóa học bằng cách click vào nút "Đăng ký" trên trang chi tiết khóa học, sau đó điền thông tin và thanh toán.</p>
                                        </div>
                                    </div>
                                    <div style="border: 1px solid #ddd; border-radius: 8px; margin-bottom: 15px;">
                                        <div style="background: #f8f9fa; padding: 20px; cursor: pointer; border-radius: 8px 8px 0 0;">
                                            <h5 style="margin: 0;">Có được hoàn tiền không?</h5>
                                        </div>
                                        <div style="padding: 20px; display: none;">
                                            <p>Chúng tôi có chính sách hoàn tiền trong vòng 7 ngày đầu tiên nếu bạn không hài lòng với khóa học.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>
                `,
                attributes: { class: 'fa fa-question-circle' }
            });

            // Call to Action
            blockManager.add('cta', {
                label: 'Call to Action',
                category: 'Extra',
                content: `
                    <section style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 80px 0; text-align: center;">
                        <div class="container">
                            <h2 style="font-size: 2.5rem; margin-bottom: 20px;">Sẵn sàng bắt đầu hành trình học tập?</h2>
                            <p style="font-size: 1.2rem; margin-bottom: 30px; max-width: 600px; margin-left: auto; margin-right: auto;">Tham gia cùng hàng nghìn học viên đã thành công với các khóa học chất lượng cao của chúng tôi.</p>
                            <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                                <a href="#" style="background: white; color: #667eea; padding: 15px 30px; border-radius: 5px; text-decoration: none; font-weight: bold; font-size: 16px;">Xem khóa học</a>
                                <a href="#" style="background: transparent; color: white; padding: 15px 30px; border: 2px solid white; border-radius: 5px; text-decoration: none; font-weight: bold; font-size: 16px;">Liên hệ tư vấn</a>
                            </div>
                        </div>
                    </section>
                `,
                attributes: { class: 'fa fa-bullhorn' }
            });
        }

        function enhanceDragDrop() {
            // Enable better drag and drop for columns
            editor.on('component:add', function(component) {
                if (component.get('attributes')['data-gjs-droppable']) {
                    component.set('droppable', true);
                    component.set('highlightable', true);

                    // Add visual feedback when dragging over
                    component.on('dragover', function() {
                        component.addStyle({'border-color': '#007bff', 'background-color': 'rgba(0,123,255,0.1)'});
                    });

                    component.on('dragleave', function() {
                        component.removeStyle(['border-color', 'background-color']);
                    });
                }
            });

            // Improve component selection
            editor.on('component:selected', function(component) {
                console.log('Selected component:', component.get('tagName'));
            });

            // Auto-clear placeholder text when content is added
            editor.on('component:add', function(component, parent) {
                if (parent && parent.get('attributes')['data-gjs-droppable']) {
                    const children = parent.components();
                    if (children.length > 0) {
                        // Remove placeholder styling when content is added
                        parent.addStyle({
                            'color': 'inherit',
                            'text-align': 'left'
                        });
                    }
                }
            });
        }

        function loadPageContent() {
            fetch(`{{ route('admin.page.grapesjs.load', $id) }}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        if (data.grapesjs_components) {
                            editor.loadProjectData(data.grapesjs_components);
                        } else if (data.grapesjs_html) {
                            editor.setComponents(data.grapesjs_html);
                            if (data.grapesjs_css) {
                                editor.setStyle(data.grapesjs_css);
                            }
                        }
                        console.log('Content loaded successfully');
                    }
                })
                .catch(error => {
                    console.error('Error loading content:', error);
                    showAlert('Có lỗi khi tải nội dung trang', 'error');
                });
        }

        function savePage() {
            if (!editor) {
                showAlert('Editor chưa được khởi tạo', 'error');
                return;
            }

            showLoading('Đang lưu trang...');

            const html = editor.getHtml();
            const css = editor.getCss();
            const components = editor.getProjectData();

            fetch(`{{ route('admin.page.grapesjs.save', $id) }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    grapesjs_html: html,
                    grapesjs_css: css,
                    grapesjs_components: components
                })
            })
            .then(response => response.json())
            .then(data => {
                hideLoading();
                if (data.success) {
                    showAlert('Trang đã được lưu thành công!', 'success');
                } else {
                    showAlert(data.message || 'Có lỗi khi lưu trang', 'error');
                }
            })
            .catch(error => {
                hideLoading();
                console.error('Error saving page:', error);
                showAlert('Có lỗi khi lưu trang', 'error');
            });
        }

        function previewPage() {
            if (!editor) {
                showAlert('Editor chưa được khởi tạo', 'error');
                return;
            }

            const html = editor.getHtml();
            const css = editor.getCss();

            const previewWindow = window.open('', '_blank');
            previewWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Xem trước trang</title>
                    <link href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" rel="stylesheet">
                    <style>${css}</style>
                </head>
                <body>
                    ${html}
                </body>
                </html>
            `);
            previewWindow.document.close();
        }

        function showLoading(message = 'Đang tải...') {
            const overlay = document.getElementById('loadingOverlay');
            overlay.querySelector('div:last-child').textContent = message;
            overlay.classList.remove('hidden');
        }

        function hideLoading() {
            document.getElementById('loadingOverlay').classList.add('hidden');
        }

        function showError(message) {
            const overlay = document.getElementById('loadingOverlay');
            overlay.innerHTML = `<div style="color: #e74c3c; text-align: center;"><h3>Lỗi</h3><p>${message}</p><button class="btn btn-secondary" onclick="location.reload()">Tải lại trang</button></div>`;
        }

        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;

            alertContainer.appendChild(alert);

            // Show alert
            setTimeout(() => {
                alert.classList.add('show');
            }, 100);

            // Hide alert after 3 seconds
            setTimeout(() => {
                alert.classList.remove('show');
                setTimeout(() => {
                    if (alertContainer.contains(alert)) {
                        alertContainer.removeChild(alert);
                    }
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>
